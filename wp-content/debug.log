[01-Jul-2025 11:15:34 UTC] AUI Hook: toplevel_page_auto-upload-image
[01-Jul-2025 11:15:34 UTC] AUI Scripts enqueued for hook: toplevel_page_auto-upload-image
[01-Jul-2025 11:15:41 UTC] AUI Debug - process_uploaded_files called with 2 files
[01-Jul-2025 11:15:41 UTC] AUI Debug - File #0: ban-chai.png
[01-Jul-2025 11:15:41 UTC] AUI Debug - File #1: ban-chai_1.png
[01-Jul-2025 11:15:41 UTC] AUI Debug - Processing upload #0:
[01-Jul-2025 11:15:41 UTC] AUI Debug - Original filename: ban-chai.png
[01-Jul-2025 11:15:41 UTC] AUI Debug - Sanitized filename: ban-chai.png
[01-Jul-2025 11:15:41 UTC] AUI Debug - Original filename: ban-chai.png
[01-Jul-2025 11:15:41 UTC] AUI Debug - Unique filename: ban-chai_1751368541_0_851.png
[01-Jul-2025 11:15:41 UTC] AUI Debug - Temp path: C:\laragon\www\reup/wp-content/uploads/aui-temp/ban-chai_1751368541_0_851.png
[01-Jul-2025 11:15:41 UTC] AUI Debug - Successfully moved file to: C:\laragon\www\reup/wp-content/uploads/aui-temp/ban-chai_1751368541_0_851.png
[01-Jul-2025 11:15:41 UTC] AUI Debug - File ID: file_0_1751368541_1000, Original: ban-chai.png, Temp: ban-chai_1751368541_0_851.png
[01-Jul-2025 11:15:41 UTC] AUI Debug - Processing upload #1:
[01-Jul-2025 11:15:41 UTC] AUI Debug - Original filename: ban-chai_1.png
[01-Jul-2025 11:15:41 UTC] AUI Debug - Sanitized filename: ban-chai_1.png
[01-Jul-2025 11:15:41 UTC] AUI Debug - Original filename: ban-chai_1.png
[01-Jul-2025 11:15:41 UTC] AUI Debug - Unique filename: ban-chai_1_1751368541_1_192.png
[01-Jul-2025 11:15:41 UTC] AUI Debug - Temp path: C:\laragon\www\reup/wp-content/uploads/aui-temp/ban-chai_1_1751368541_1_192.png
[01-Jul-2025 11:15:41 UTC] AUI Debug - Successfully moved file to: C:\laragon\www\reup/wp-content/uploads/aui-temp/ban-chai_1_1751368541_1_192.png
[01-Jul-2025 11:15:41 UTC] AUI Debug - File ID: file_1_1751368541_5876, Original: ban-chai_1.png, Temp: ban-chai_1_1751368541_1_192.png
[01-Jul-2025 11:15:41 UTC] AUI Debug - Files after sorting:
[01-Jul-2025 11:15:41 UTC] AUI Debug - Sorted file: ban-chai.png
[01-Jul-2025 11:15:41 UTC] AUI Debug - Sorted file: ban-chai_1.png
[01-Jul-2025 11:15:41 UTC] AUI Debug - extract_base_name: 'ban-chai' -> no pattern, using full name
[01-Jul-2025 11:15:41 UTC] AUI Debug - Processing file: ban-chai.png (ID: file_0_1751368541_1000), base: ban-chai
[01-Jul-2025 11:15:41 UTC] AUI Debug - Added file file_0_1751368541_1000 to current group (base: ban-chai)
[01-Jul-2025 11:15:41 UTC] AUI Debug - extract_base_name: 'ban-chai_1' -> base: 'ban-chai', number: '1'
[01-Jul-2025 11:15:41 UTC] AUI Debug - Processing file: ban-chai_1.png (ID: file_1_1751368541_5876), base: ban-chai
[01-Jul-2025 11:15:41 UTC] AUI Debug - Added file file_1_1751368541_5876 to current group (base: ban-chai)
[01-Jul-2025 11:15:41 UTC] AUI Debug - Added final group with 2 files
[01-Jul-2025 11:15:41 UTC] AUI Debug - No simple number pattern in 'ban-chai', using 0
[01-Jul-2025 11:15:41 UTC] AUI Debug - Extracted number 1 from 'ban-chai_1'
[01-Jul-2025 11:15:41 UTC] AUI Debug - Group 0 final order:
[01-Jul-2025 11:15:41 UTC] AUI Debug - ban-chai.png
[01-Jul-2025 11:15:41 UTC] AUI Debug - ban-chai_1.png
[01-Jul-2025 11:15:41 UTC] AUI Debug - Final grouped images:
[01-Jul-2025 11:15:41 UTC] AUI Debug - Group 0 (2 files): ban-chai.png, ban-chai_1.png
[01-Jul-2025 11:15:41 UTC] AUI Debug - Total groups: 1
[01-Jul-2025 11:15:41 UTC] AUI Debug - Original files count: 2
[01-Jul-2025 11:15:41 UTC] AUI Debug - Total files in groups: 2
[01-Jul-2025 11:15:41 UTC] AUI Debug - Starting upload for group 0 with 2 files
[01-Jul-2025 11:15:41 UTC] AUI Debug - Uploading group of 2 files to domain: https://cn-av.com
[01-Jul-2025 11:15:41 UTC] AUI Debug - Group file #0: ban-chai.png (path: C:\laragon\www\reup/wp-content/uploads/aui-temp/ban-chai_1751368541_0_851.png)
[01-Jul-2025 11:15:41 UTC] AUI Debug - Group file #1: ban-chai_1.png (path: C:\laragon\www\reup/wp-content/uploads/aui-temp/ban-chai_1_1751368541_1_192.png)
[01-Jul-2025 11:15:41 UTC] AUI Debug - Starting upload file #0: ban-chai.png (ID: file_0_1751368541_1000)
[01-Jul-2025 11:15:41 UTC] AUI Debug - File path: C:\laragon\www\reup/wp-content/uploads/aui-temp/ban-chai_1751368541_0_851.png
[01-Jul-2025 11:15:41 UTC] AUI Debug - File URL: http://reup.test/wp-content/uploads/aui-temp/ban-chai_1751368541_0_851.png
[01-Jul-2025 11:15:41 UTC] AUI Debug - upload_single_image called for: ban-chai.png
[01-Jul-2025 11:15:41 UTC] AUI Debug - File path: C:\laragon\www\reup/wp-content/uploads/aui-temp/ban-chai_1751368541_0_851.png
[01-Jul-2025 11:15:41 UTC] AUI Debug - File URL: http://reup.test/wp-content/uploads/aui-temp/ban-chai_1751368541_0_851.png
[01-Jul-2025 11:15:41 UTC] AUI Debug - File identifier: file_0_1751368541_1000
[01-Jul-2025 11:15:41 UTC] AUI Debug - File ban-chai.png (ID: file_0_1751368541_1000) is NEW, proceeding with upload
[01-Jul-2025 11:15:41 UTC] AUI Debug - Current tracker keys: 
[01-Jul-2025 11:15:41 UTC] AUI Debug - Original filename: ban-chai.png
[01-Jul-2025 11:15:41 UTC] AUI Debug - Name without ext: ban-chai
[01-Jul-2025 11:15:41 UTC] AUI Debug - API name: ban-chai
[01-Jul-2025 11:15:41 UTC] AUI Debug - Extension: png
[01-Jul-2025 11:15:41 UTC] AUI Debug - API data prepared: {"upload_auto_delete":0,"type":"image\/png","nameFile":"ban-chai","extFile":"png","linkImage":"http:\/\/reup.test\/wp-content\/uploads\/aui-temp\/ban-chai_1751368541_0_851.png","size":385141}
[01-Jul-2025 11:15:41 UTC] AUI Debug - API Failed for ban-chai.png: HTTP 403: <!DOCTYPE html>
<!--[if lt IE 7]> <html class="no-js ie6 oldie" lang="en-US"> <![endif]-->
<!--[if IE 7]>    <html class="no-js ie7 oldie" lang="en-US"> <![endif]-->
<!--[if IE 8]>    <html class="no-js ie8 oldie" lang="en-US"> <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="en-US"> <!--<![endif]-->
<head>
<title>Attention Required! | Cloudflare</title>
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta http-equiv="X-UA-Compatible" content="IE=Edge" />
<meta name="robots" content="noindex, nofollow" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<link rel="stylesheet" id="cf_styles-css" href="/cdn-cgi/styles/cf.errors.css" />
<!--[if lt IE 9]><link rel="stylesheet" id='cf_styles-ie-css' href="/cdn-cgi/styles/cf.errors.ie.css" /><![endif]-->
<style>body{margin:0;padding:0}</style>


<!--[if gte IE 10]><!-->
<script>
  if (!navigator.cookieEnabled) {
    window.addEventListener('DOMContentLoaded', function () {
      var cookieEl = document.getElementById('cookie-alert');
      cookieEl.style.display = 'block';
    })
  }
</script>
<!--<![endif]-->

</head>
<body>
  <div id="cf-wrapper">
    <div class="cf-alert cf-alert-error cf-cookie-error" id="cookie-alert" data-translate="enable_cookies">Please enable cookies.</div>
    <div id="cf-error-details" class="cf-error-details-wrapper">
      <div class="cf-wrapper cf-header cf-error-overview">
        <h1 data-translate="block_headline">Sorry, you have been blocked</h1>
        <h2 class="cf-subheadline"><span data-translate="unable_to_access">You are unable to access</span> cn-av.com</h2>
      </div><!-- /.header -->

      <div class="cf-section cf-highlight">
        <div class="cf-wrapper">
          <div class="cf-screenshot-container cf-screenshot-full">
            
              <span class="cf-no-screenshot error"></span>
            
          </div>
        </div>
      </div><!-- /.captcha-container -->

      <div class="cf-section cf-wrapper">
        <div class="cf-columns two">
          <div class="cf-column">
            <h2 data-translate="blocked_why_headline">Why have I been blocked?</h2>

            <p data-translate="blocked_why_detail">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.</p>
          </div>

          <div class="cf-column">
            <h2 data-translate="blocked_resolve_headline">What can I do to resolve this?</h2>

            <p data-translate="blocked_resolve_detail">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.</p>
          </div>
        </div>
      </div><!-- /.section -->

      <div class="cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300">
    <p class="text-13">
      <span class="cf-footer-item sm:block sm:mb-1">Cloudflare Ray ID: <strong class="font-semibold">95857cb44c148497</strong></span>
      <span class="cf-footer-separator sm:hidden">&bull;</span>
      <span id="cf-footer-item-ip" class="cf-footer-item hidden sm:block sm:mb-1">
        Your IP:
        <button type="button" id="cf-footer-ip-reveal" class="cf-footer-ip-reveal-btn">Click to reveal</button>
        <span class="hidden" id="cf-footer-ip">*************</span>
        <span class="cf-footer-separator sm:hidden">&bull;</span>
      </span>
      <span class="cf-footer-item sm:block sm:mb-1"><span>Performance &amp; security by</span> <a rel="noopener noreferrer" href="https://www.cloudflare.com/5xx-error-landing" id="brand_link" target="_blank">Cloudflare</a></span>
      
    </p>
    <script>(function(){function d(){var b=a.getElementById("cf-footer-item-ip"),c=a.getElementById("cf-footer-ip-reveal");b&&"classList"in b&&(b.classList.remove("hidden"),c.addEventListener("click",function(){c.classList.add("hidden");a.getElementById("cf-footer-ip").classList.remove("hidden")}))}var a=document;document.addEventListener&&a.addEventListener("DOMContentLoaded",d)})();</script>
  </div><!-- /.error-footer -->

    </div><!-- /#cf-error-details -->
  </div><!-- /#cf-wrapper -->

  <script>
    window._cf_translation = {};
    
    
  </script>
</body>
</html>
[01-Jul-2025 11:15:42 UTC] AUI Debug - API Failed for ban-chai.png: HTTP 403: <!DOCTYPE html>
<!--[if lt IE 7]> <html class="no-js ie6 oldie" lang="en-US"> <![endif]-->
<!--[if IE 7]>    <html class="no-js ie7 oldie" lang="en-US"> <![endif]-->
<!--[if IE 8]>    <html class="no-js ie8 oldie" lang="en-US"> <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="en-US"> <!--<![endif]-->
<head>
<title>Attention Required! | Cloudflare</title>
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta http-equiv="X-UA-Compatible" content="IE=Edge" />
<meta name="robots" content="noindex, nofollow" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<link rel="stylesheet" id="cf_styles-css" href="/cdn-cgi/styles/cf.errors.css" />
<!--[if lt IE 9]><link rel="stylesheet" id='cf_styles-ie-css' href="/cdn-cgi/styles/cf.errors.ie.css" /><![endif]-->
<style>body{margin:0;padding:0}</style>


<!--[if gte IE 10]><!-->
<script>
  if (!navigator.cookieEnabled) {
    window.addEventListener('DOMContentLoaded', function () {
      var cookieEl = document.getElementById('cookie-alert');
      cookieEl.style.display = 'block';
    })
  }
</script>
<!--<![endif]-->

</head>
<body>
  <div id="cf-wrapper">
    <div class="cf-alert cf-alert-error cf-cookie-error" id="cookie-alert" data-translate="enable_cookies">Please enable cookies.</div>
    <div id="cf-error-details" class="cf-error-details-wrapper">
      <div class="cf-wrapper cf-header cf-error-overview">
        <h1 data-translate="block_headline">Sorry, you have been blocked</h1>
        <h2 class="cf-subheadline"><span data-translate="unable_to_access">You are unable to access</span> cn-av.com</h2>
      </div><!-- /.header -->

      <div class="cf-section cf-highlight">
        <div class="cf-wrapper">
          <div class="cf-screenshot-container cf-screenshot-full">
            
              <span class="cf-no-screenshot error"></span>
            
          </div>
        </div>
      </div><!-- /.captcha-container -->

      <div class="cf-section cf-wrapper">
        <div class="cf-columns two">
          <div class="cf-column">
            <h2 data-translate="blocked_why_headline">Why have I been blocked?</h2>

            <p data-translate="blocked_why_detail">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.</p>
          </div>

          <div class="cf-column">
            <h2 data-translate="blocked_resolve_headline">What can I do to resolve this?</h2>

            <p data-translate="blocked_resolve_detail">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.</p>
          </div>
        </div>
      </div><!-- /.section -->

      <div class="cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300">
    <p class="text-13">
      <span class="cf-footer-item sm:block sm:mb-1">Cloudflare Ray ID: <strong class="font-semibold">95857cbb1c9b8b81</strong></span>
      <span class="cf-footer-separator sm:hidden">&bull;</span>
      <span id="cf-footer-item-ip" class="cf-footer-item hidden sm:block sm:mb-1">
        Your IP:
        <button type="button" id="cf-footer-ip-reveal" class="cf-footer-ip-reveal-btn">Click to reveal</button>
        <span class="hidden" id="cf-footer-ip">*************</span>
        <span class="cf-footer-separator sm:hidden">&bull;</span>
      </span>
      <span class="cf-footer-item sm:block sm:mb-1"><span>Performance &amp; security by</span> <a rel="noopener noreferrer" href="https://www.cloudflare.com/5xx-error-landing" id="brand_link" target="_blank">Cloudflare</a></span>
      
    </p>
    <script>(function(){function d(){var b=a.getElementById("cf-footer-item-ip"),c=a.getElementById("cf-footer-ip-reveal");b&&"classList"in b&&(b.classList.remove("hidden"),c.addEventListener("click",function(){c.classList.add("hidden");a.getElementById("cf-footer-ip").classList.remove("hidden")}))}var a=document;document.addEventListener&&a.addEventListener("DOMContentLoaded",d)})();</script>
  </div><!-- /.error-footer -->

    </div><!-- /#cf-error-details -->
  </div><!-- /#cf-wrapper -->

  <script>
    window._cf_translation = {};
    
    
  </script>
</body>
</html>
[01-Jul-2025 11:15:43 UTC] AUI Debug - API Failed for ban-chai.png: HTTP 403: <!DOCTYPE html>
<!--[if lt IE 7]> <html class="no-js ie6 oldie" lang="en-US"> <![endif]-->
<!--[if IE 7]>    <html class="no-js ie7 oldie" lang="en-US"> <![endif]-->
<!--[if IE 8]>    <html class="no-js ie8 oldie" lang="en-US"> <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="en-US"> <!--<![endif]-->
<head>
<title>Attention Required! | Cloudflare</title>
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta http-equiv="X-UA-Compatible" content="IE=Edge" />
<meta name="robots" content="noindex, nofollow" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<link rel="stylesheet" id="cf_styles-css" href="/cdn-cgi/styles/cf.errors.css" />
<!--[if lt IE 9]><link rel="stylesheet" id='cf_styles-ie-css' href="/cdn-cgi/styles/cf.errors.ie.css" /><![endif]-->
<style>body{margin:0;padding:0}</style>


<!--[if gte IE 10]><!-->
<script>
  if (!navigator.cookieEnabled) {
    window.addEventListener('DOMContentLoaded', function () {
      var cookieEl = document.getElementById('cookie-alert');
      cookieEl.style.display = 'block';
    })
  }
</script>
<!--<![endif]-->

</head>
<body>
  <div id="cf-wrapper">
    <div class="cf-alert cf-alert-error cf-cookie-error" id="cookie-alert" data-translate="enable_cookies">Please enable cookies.</div>
    <div id="cf-error-details" class="cf-error-details-wrapper">
      <div class="cf-wrapper cf-header cf-error-overview">
        <h1 data-translate="block_headline">Sorry, you have been blocked</h1>
        <h2 class="cf-subheadline"><span data-translate="unable_to_access">You are unable to access</span> cn-av.com</h2>
      </div><!-- /.header -->

      <div class="cf-section cf-highlight">
        <div class="cf-wrapper">
          <div class="cf-screenshot-container cf-screenshot-full">
            
              <span class="cf-no-screenshot error"></span>
            
          </div>
        </div>
      </div><!-- /.captcha-container -->

      <div class="cf-section cf-wrapper">
        <div class="cf-columns two">
          <div class="cf-column">
            <h2 data-translate="blocked_why_headline">Why have I been blocked?</h2>

            <p data-translate="blocked_why_detail">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.</p>
          </div>

          <div class="cf-column">
            <h2 data-translate="blocked_resolve_headline">What can I do to resolve this?</h2>

            <p data-translate="blocked_resolve_detail">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.</p>
          </div>
        </div>
      </div><!-- /.section -->

      <div class="cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300">
    <p class="text-13">
      <span class="cf-footer-item sm:block sm:mb-1">Cloudflare Ray ID: <strong class="font-semibold">95857cc20ac0e2ec</strong></span>
      <span class="cf-footer-separator sm:hidden">&bull;</span>
      <span id="cf-footer-item-ip" class="cf-footer-item hidden sm:block sm:mb-1">
        Your IP:
        <button type="button" id="cf-footer-ip-reveal" class="cf-footer-ip-reveal-btn">Click to reveal</button>
        <span class="hidden" id="cf-footer-ip">*************</span>
        <span class="cf-footer-separator sm:hidden">&bull;</span>
      </span>
      <span class="cf-footer-item sm:block sm:mb-1"><span>Performance &amp; security by</span> <a rel="noopener noreferrer" href="https://www.cloudflare.com/5xx-error-landing" id="brand_link" target="_blank">Cloudflare</a></span>
      
    </p>
    <script>(function(){function d(){var b=a.getElementById("cf-footer-item-ip"),c=a.getElementById("cf-footer-ip-reveal");b&&"classList"in b&&(b.classList.remove("hidden"),c.addEventListener("click",function(){c.classList.add("hidden");a.getElementById("cf-footer-ip").classList.remove("hidden")}))}var a=document;document.addEventListener&&a.addEventListener("DOMContentLoaded",d)})();</script>
  </div><!-- /.error-footer -->

    </div><!-- /#cf-error-details -->
  </div><!-- /#cf-wrapper -->

  <script>
    window._cf_translation = {};
    
    
  </script>
</body>
</html>
[01-Jul-2025 11:15:43 UTC] AUI Debug - Upload completed for ban-chai.png: FAILED
[01-Jul-2025 11:15:43 UTC] AUI Debug - Starting upload file #1: ban-chai_1.png (ID: file_1_1751368541_5876)
[01-Jul-2025 11:15:43 UTC] AUI Debug - File path: C:\laragon\www\reup/wp-content/uploads/aui-temp/ban-chai_1_1751368541_1_192.png
[01-Jul-2025 11:15:43 UTC] AUI Debug - File URL: http://reup.test/wp-content/uploads/aui-temp/ban-chai_1_1751368541_1_192.png
[01-Jul-2025 11:15:44 UTC] AUI Debug - upload_single_image called for: ban-chai_1.png
[01-Jul-2025 11:15:44 UTC] AUI Debug - File path: C:\laragon\www\reup/wp-content/uploads/aui-temp/ban-chai_1_1751368541_1_192.png
[01-Jul-2025 11:15:44 UTC] AUI Debug - File URL: http://reup.test/wp-content/uploads/aui-temp/ban-chai_1_1751368541_1_192.png
[01-Jul-2025 11:15:44 UTC] AUI Debug - File identifier: file_1_1751368541_5876
[01-Jul-2025 11:15:44 UTC] AUI Debug - File ban-chai_1.png (ID: file_1_1751368541_5876) is NEW, proceeding with upload
[01-Jul-2025 11:15:44 UTC] AUI Debug - Current tracker keys: file_0_1751368541_1000
[01-Jul-2025 11:15:44 UTC] AUI Debug - Original filename: ban-chai_1.png
[01-Jul-2025 11:15:44 UTC] AUI Debug - Name without ext: ban-chai_1
[01-Jul-2025 11:15:44 UTC] AUI Debug - API name: ban-chai_1
[01-Jul-2025 11:15:44 UTC] AUI Debug - Extension: png
[01-Jul-2025 11:15:44 UTC] AUI Debug - API data prepared: {"upload_auto_delete":0,"type":"image\/png","nameFile":"ban-chai_1","extFile":"png","linkImage":"http:\/\/reup.test\/wp-content\/uploads\/aui-temp\/ban-chai_1_1751368541_1_192.png","size":598799}
[01-Jul-2025 11:15:44 UTC] AUI Debug - API Failed for ban-chai_1.png: HTTP 403: <!DOCTYPE html>
<!--[if lt IE 7]> <html class="no-js ie6 oldie" lang="en-US"> <![endif]-->
<!--[if IE 7]>    <html class="no-js ie7 oldie" lang="en-US"> <![endif]-->
<!--[if IE 8]>    <html class="no-js ie8 oldie" lang="en-US"> <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="en-US"> <!--<![endif]-->
<head>
<title>Attention Required! | Cloudflare</title>
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta http-equiv="X-UA-Compatible" content="IE=Edge" />
<meta name="robots" content="noindex, nofollow" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<link rel="stylesheet" id="cf_styles-css" href="/cdn-cgi/styles/cf.errors.css" />
<!--[if lt IE 9]><link rel="stylesheet" id='cf_styles-ie-css' href="/cdn-cgi/styles/cf.errors.ie.css" /><![endif]-->
<style>body{margin:0;padding:0}</style>


<!--[if gte IE 10]><!-->
<script>
  if (!navigator.cookieEnabled) {
    window.addEventListener('DOMContentLoaded', function () {
      var cookieEl = document.getElementById('cookie-alert');
      cookieEl.style.display = 'block';
    })
  }
</script>
<!--<![endif]-->

</head>
<body>
  <div id="cf-wrapper">
    <div class="cf-alert cf-alert-error cf-cookie-error" id="cookie-alert" data-translate="enable_cookies">Please enable cookies.</div>
    <div id="cf-error-details" class="cf-error-details-wrapper">
      <div class="cf-wrapper cf-header cf-error-overview">
        <h1 data-translate="block_headline">Sorry, you have been blocked</h1>
        <h2 class="cf-subheadline"><span data-translate="unable_to_access">You are unable to access</span> cn-av.com</h2>
      </div><!-- /.header -->

      <div class="cf-section cf-highlight">
        <div class="cf-wrapper">
          <div class="cf-screenshot-container cf-screenshot-full">
            
              <span class="cf-no-screenshot error"></span>
            
          </div>
        </div>
      </div><!-- /.captcha-container -->

      <div class="cf-section cf-wrapper">
        <div class="cf-columns two">
          <div class="cf-column">
            <h2 data-translate="blocked_why_headline">Why have I been blocked?</h2>

            <p data-translate="blocked_why_detail">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.</p>
          </div>

          <div class="cf-column">
            <h2 data-translate="blocked_resolve_headline">What can I do to resolve this?</h2>

            <p data-translate="blocked_resolve_detail">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.</p>
          </div>
        </div>
      </div><!-- /.section -->

      <div class="cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300">
    <p class="text-13">
      <span class="cf-footer-item sm:block sm:mb-1">Cloudflare Ray ID: <strong class="font-semibold">95857cc8e8039b47</strong></span>
      <span class="cf-footer-separator sm:hidden">&bull;</span>
      <span id="cf-footer-item-ip" class="cf-footer-item hidden sm:block sm:mb-1">
        Your IP:
        <button type="button" id="cf-footer-ip-reveal" class="cf-footer-ip-reveal-btn">Click to reveal</button>
        <span class="hidden" id="cf-footer-ip">*************</span>
        <span class="cf-footer-separator sm:hidden">&bull;</span>
      </span>
      <span class="cf-footer-item sm:block sm:mb-1"><span>Performance &amp; security by</span> <a rel="noopener noreferrer" href="https://www.cloudflare.com/5xx-error-landing" id="brand_link" target="_blank">Cloudflare</a></span>
      
    </p>
    <script>(function(){function d(){var b=a.getElementById("cf-footer-item-ip"),c=a.getElementById("cf-footer-ip-reveal");b&&"classList"in b&&(b.classList.remove("hidden"),c.addEventListener("click",function(){c.classList.add("hidden");a.getElementById("cf-footer-ip").classList.remove("hidden")}))}var a=document;document.addEventListener&&a.addEventListener("DOMContentLoaded",d)})();</script>
  </div><!-- /.error-footer -->

    </div><!-- /#cf-error-details -->
  </div><!-- /#cf-wrapper -->

  <script>
    window._cf_translation = {};
    
    
  </script>
</body>
</html>
[01-Jul-2025 11:15:45 UTC] AUI Debug - API Failed for ban-chai_1.png: HTTP 403: <!DOCTYPE html>
<!--[if lt IE 7]> <html class="no-js ie6 oldie" lang="en-US"> <![endif]-->
<!--[if IE 7]>    <html class="no-js ie7 oldie" lang="en-US"> <![endif]-->
<!--[if IE 8]>    <html class="no-js ie8 oldie" lang="en-US"> <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="en-US"> <!--<![endif]-->
<head>
<title>Attention Required! | Cloudflare</title>
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta http-equiv="X-UA-Compatible" content="IE=Edge" />
<meta name="robots" content="noindex, nofollow" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<link rel="stylesheet" id="cf_styles-css" href="/cdn-cgi/styles/cf.errors.css" />
<!--[if lt IE 9]><link rel="stylesheet" id='cf_styles-ie-css' href="/cdn-cgi/styles/cf.errors.ie.css" /><![endif]-->
<style>body{margin:0;padding:0}</style>


<!--[if gte IE 10]><!-->
<script>
  if (!navigator.cookieEnabled) {
    window.addEventListener('DOMContentLoaded', function () {
      var cookieEl = document.getElementById('cookie-alert');
      cookieEl.style.display = 'block';
    })
  }
</script>
<!--<![endif]-->

</head>
<body>
  <div id="cf-wrapper">
    <div class="cf-alert cf-alert-error cf-cookie-error" id="cookie-alert" data-translate="enable_cookies">Please enable cookies.</div>
    <div id="cf-error-details" class="cf-error-details-wrapper">
      <div class="cf-wrapper cf-header cf-error-overview">
        <h1 data-translate="block_headline">Sorry, you have been blocked</h1>
        <h2 class="cf-subheadline"><span data-translate="unable_to_access">You are unable to access</span> cn-av.com</h2>
      </div><!-- /.header -->

      <div class="cf-section cf-highlight">
        <div class="cf-wrapper">
          <div class="cf-screenshot-container cf-screenshot-full">
            
              <span class="cf-no-screenshot error"></span>
            
          </div>
        </div>
      </div><!-- /.captcha-container -->

      <div class="cf-section cf-wrapper">
        <div class="cf-columns two">
          <div class="cf-column">
            <h2 data-translate="blocked_why_headline">Why have I been blocked?</h2>

            <p data-translate="blocked_why_detail">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.</p>
          </div>

          <div class="cf-column">
            <h2 data-translate="blocked_resolve_headline">What can I do to resolve this?</h2>

            <p data-translate="blocked_resolve_detail">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.</p>
          </div>
        </div>
      </div><!-- /.section -->

      <div class="cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300">
    <p class="text-13">
      <span class="cf-footer-item sm:block sm:mb-1">Cloudflare Ray ID: <strong class="font-semibold">95857ccfed008554</strong></span>
      <span class="cf-footer-separator sm:hidden">&bull;</span>
      <span id="cf-footer-item-ip" class="cf-footer-item hidden sm:block sm:mb-1">
        Your IP:
        <button type="button" id="cf-footer-ip-reveal" class="cf-footer-ip-reveal-btn">Click to reveal</button>
        <span class="hidden" id="cf-footer-ip">*************</span>
        <span class="cf-footer-separator sm:hidden">&bull;</span>
      </span>
      <span class="cf-footer-item sm:block sm:mb-1"><span>Performance &amp; security by</span> <a rel="noopener noreferrer" href="https://www.cloudflare.com/5xx-error-landing" id="brand_link" target="_blank">Cloudflare</a></span>
      
    </p>
    <script>(function(){function d(){var b=a.getElementById("cf-footer-item-ip"),c=a.getElementById("cf-footer-ip-reveal");b&&"classList"in b&&(b.classList.remove("hidden"),c.addEventListener("click",function(){c.classList.add("hidden");a.getElementById("cf-footer-ip").classList.remove("hidden")}))}var a=document;document.addEventListener&&a.addEventListener("DOMContentLoaded",d)})();</script>
  </div><!-- /.error-footer -->

    </div><!-- /#cf-error-details -->
  </div><!-- /#cf-wrapper -->

  <script>
    window._cf_translation = {};
    
    
  </script>
</body>
</html>
[01-Jul-2025 11:15:46 UTC] AUI Debug - API Failed for ban-chai_1.png: HTTP 403: <!DOCTYPE html>
<!--[if lt IE 7]> <html class="no-js ie6 oldie" lang="en-US"> <![endif]-->
<!--[if IE 7]>    <html class="no-js ie7 oldie" lang="en-US"> <![endif]-->
<!--[if IE 8]>    <html class="no-js ie8 oldie" lang="en-US"> <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="en-US"> <!--<![endif]-->
<head>
<title>Attention Required! | Cloudflare</title>
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta http-equiv="X-UA-Compatible" content="IE=Edge" />
<meta name="robots" content="noindex, nofollow" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<link rel="stylesheet" id="cf_styles-css" href="/cdn-cgi/styles/cf.errors.css" />
<!--[if lt IE 9]><link rel="stylesheet" id='cf_styles-ie-css' href="/cdn-cgi/styles/cf.errors.ie.css" /><![endif]-->
<style>body{margin:0;padding:0}</style>


<!--[if gte IE 10]><!-->
<script>
  if (!navigator.cookieEnabled) {
    window.addEventListener('DOMContentLoaded', function () {
      var cookieEl = document.getElementById('cookie-alert');
      cookieEl.style.display = 'block';
    })
  }
</script>
<!--<![endif]-->

</head>
<body>
  <div id="cf-wrapper">
    <div class="cf-alert cf-alert-error cf-cookie-error" id="cookie-alert" data-translate="enable_cookies">Please enable cookies.</div>
    <div id="cf-error-details" class="cf-error-details-wrapper">
      <div class="cf-wrapper cf-header cf-error-overview">
        <h1 data-translate="block_headline">Sorry, you have been blocked</h1>
        <h2 class="cf-subheadline"><span data-translate="unable_to_access">You are unable to access</span> cn-av.com</h2>
      </div><!-- /.header -->

      <div class="cf-section cf-highlight">
        <div class="cf-wrapper">
          <div class="cf-screenshot-container cf-screenshot-full">
            
              <span class="cf-no-screenshot error"></span>
            
          </div>
        </div>
      </div><!-- /.captcha-container -->

      <div class="cf-section cf-wrapper">
        <div class="cf-columns two">
          <div class="cf-column">
            <h2 data-translate="blocked_why_headline">Why have I been blocked?</h2>

            <p data-translate="blocked_why_detail">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.</p>
          </div>

          <div class="cf-column">
            <h2 data-translate="blocked_resolve_headline">What can I do to resolve this?</h2>

            <p data-translate="blocked_resolve_detail">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.</p>
          </div>
        </div>
      </div><!-- /.section -->

      <div class="cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300">
    <p class="text-13">
      <span class="cf-footer-item sm:block sm:mb-1">Cloudflare Ray ID: <strong class="font-semibold">95857cd6dc5e08b3</strong></span>
      <span class="cf-footer-separator sm:hidden">&bull;</span>
      <span id="cf-footer-item-ip" class="cf-footer-item hidden sm:block sm:mb-1">
        Your IP:
        <button type="button" id="cf-footer-ip-reveal" class="cf-footer-ip-reveal-btn">Click to reveal</button>
        <span class="hidden" id="cf-footer-ip">*************</span>
        <span class="cf-footer-separator sm:hidden">&bull;</span>
      </span>
      <span class="cf-footer-item sm:block sm:mb-1"><span>Performance &amp; security by</span> <a rel="noopener noreferrer" href="https://www.cloudflare.com/5xx-error-landing" id="brand_link" target="_blank">Cloudflare</a></span>
      
    </p>
    <script>(function(){function d(){var b=a.getElementById("cf-footer-item-ip"),c=a.getElementById("cf-footer-ip-reveal");b&&"classList"in b&&(b.classList.remove("hidden"),c.addEventListener("click",function(){c.classList.add("hidden");a.getElementById("cf-footer-ip").classList.remove("hidden")}))}var a=document;document.addEventListener&&a.addEventListener("DOMContentLoaded",d)})();</script>
  </div><!-- /.error-footer -->

    </div><!-- /#cf-error-details -->
  </div><!-- /#cf-wrapper -->

  <script>
    window._cf_translation = {};
    
    
  </script>
</body>
</html>
[01-Jul-2025 11:15:46 UTC] AUI Debug - Upload completed for ban-chai_1.png: FAILED
[01-Jul-2025 11:15:46 UTC] AUI Debug - Group upload completed. Total results: 2
[01-Jul-2025 11:15:46 UTC] AUI Debug - Group result #0: ban-chai.png (ID: file_0_1751368541_1000) - FAILED
[01-Jul-2025 11:15:46 UTC] AUI Debug - Group result #1: ban-chai_1.png (ID: file_1_1751368541_5876) - FAILED
[01-Jul-2025 11:15:46 UTC] AUI Debug - Group 0 upload completed, got 2 results
[01-Jul-2025 11:15:46 UTC] AUI Debug - Result for ban-chai.png (ID: file_0_1751368541_1000): FAILED
[01-Jul-2025 11:15:46 UTC] AUI Debug - Result for ban-chai_1.png (ID: file_1_1751368541_5876): FAILED
[01-Jul-2025 11:15:46 UTC] AUI Debug - Added result for ban-chai.png (ID: file_0_1751368541_1000)
[01-Jul-2025 11:15:46 UTC] AUI Debug - DUPLICATE DETECTED! Skipping result for ban-chai.png (ID: file_0_1751368541_1000)
[01-Jul-2025 11:15:46 UTC] AUI Debug - Total results: 1
[01-Jul-2025 11:15:46 UTC] AUI Debug - Successful: 0
[01-Jul-2025 11:15:46 UTC] AUI Debug - Failed: 1
[01-Jul-2025 11:15:46 UTC] AUI Debug - Final results being sent to client:
[01-Jul-2025 11:15:46 UTC] AUI Debug - Final result #0: ban-chai.png (ID: file_0_1751368541_1000)
[01-Jul-2025 11:16:56 UTC] AUI Debug - process_uploaded_files called with 2 files
[01-Jul-2025 11:16:56 UTC] AUI Debug - File #0: ban-chai.png
[01-Jul-2025 11:16:56 UTC] AUI Debug - File #1: ban-chai_1.png
[01-Jul-2025 11:16:56 UTC] AUI Debug - Processing upload #0:
[01-Jul-2025 11:16:56 UTC] AUI Debug - Original filename: ban-chai.png
[01-Jul-2025 11:16:56 UTC] AUI Debug - Sanitized filename: ban-chai.png
[01-Jul-2025 11:16:56 UTC] AUI Debug - Original filename: ban-chai.png
[01-Jul-2025 11:16:56 UTC] AUI Debug - Unique filename: ban-chai_1751368616_0_592.png
[01-Jul-2025 11:16:56 UTC] AUI Debug - Temp path: C:\laragon\www\reup/wp-content/uploads/aui-temp/ban-chai_1751368616_0_592.png
[01-Jul-2025 11:16:56 UTC] AUI Debug - Successfully moved file to: C:\laragon\www\reup/wp-content/uploads/aui-temp/ban-chai_1751368616_0_592.png
[01-Jul-2025 11:16:56 UTC] AUI Debug - File ID: file_0_1751368616_8424, Original: ban-chai.png, Temp: ban-chai_1751368616_0_592.png
[01-Jul-2025 11:16:56 UTC] AUI Debug - Processing upload #1:
[01-Jul-2025 11:16:56 UTC] AUI Debug - Original filename: ban-chai_1.png
[01-Jul-2025 11:16:56 UTC] AUI Debug - Sanitized filename: ban-chai_1.png
[01-Jul-2025 11:16:56 UTC] AUI Debug - Original filename: ban-chai_1.png
[01-Jul-2025 11:16:56 UTC] AUI Debug - Unique filename: ban-chai_1_1751368616_1_616.png
[01-Jul-2025 11:16:56 UTC] AUI Debug - Temp path: C:\laragon\www\reup/wp-content/uploads/aui-temp/ban-chai_1_1751368616_1_616.png
[01-Jul-2025 11:16:56 UTC] AUI Debug - Successfully moved file to: C:\laragon\www\reup/wp-content/uploads/aui-temp/ban-chai_1_1751368616_1_616.png
[01-Jul-2025 11:16:56 UTC] AUI Debug - File ID: file_1_1751368616_6362, Original: ban-chai_1.png, Temp: ban-chai_1_1751368616_1_616.png
[01-Jul-2025 11:16:56 UTC] AUI Debug - Files after sorting:
[01-Jul-2025 11:16:56 UTC] AUI Debug - Sorted file: ban-chai.png
[01-Jul-2025 11:16:56 UTC] AUI Debug - Sorted file: ban-chai_1.png
[01-Jul-2025 11:16:56 UTC] AUI Debug - extract_base_name: 'ban-chai' -> no pattern, using full name
[01-Jul-2025 11:16:56 UTC] AUI Debug - Processing file: ban-chai.png (ID: file_0_1751368616_8424), base: ban-chai
[01-Jul-2025 11:16:56 UTC] AUI Debug - Added file file_0_1751368616_8424 to current group (base: ban-chai)
[01-Jul-2025 11:16:56 UTC] AUI Debug - extract_base_name: 'ban-chai_1' -> base: 'ban-chai', number: '1'
[01-Jul-2025 11:16:56 UTC] AUI Debug - Processing file: ban-chai_1.png (ID: file_1_1751368616_6362), base: ban-chai
[01-Jul-2025 11:16:56 UTC] AUI Debug - Added file file_1_1751368616_6362 to current group (base: ban-chai)
[01-Jul-2025 11:16:56 UTC] AUI Debug - Added final group with 2 files
[01-Jul-2025 11:16:56 UTC] AUI Debug - No simple number pattern in 'ban-chai', using 0
[01-Jul-2025 11:16:56 UTC] AUI Debug - Extracted number 1 from 'ban-chai_1'
[01-Jul-2025 11:16:56 UTC] AUI Debug - Group 0 final order:
[01-Jul-2025 11:16:56 UTC] AUI Debug - ban-chai.png
[01-Jul-2025 11:16:56 UTC] AUI Debug - ban-chai_1.png
[01-Jul-2025 11:16:56 UTC] AUI Debug - Final grouped images:
[01-Jul-2025 11:16:56 UTC] AUI Debug - Group 0 (2 files): ban-chai.png, ban-chai_1.png
[01-Jul-2025 11:16:56 UTC] AUI Debug - Total groups: 1
[01-Jul-2025 11:16:56 UTC] AUI Debug - Original files count: 2
[01-Jul-2025 11:16:56 UTC] AUI Debug - Total files in groups: 2
[01-Jul-2025 11:16:56 UTC] AUI Debug - Starting upload for group 0 with 2 files
[01-Jul-2025 11:16:56 UTC] AUI Debug - Uploading group of 2 files to domain: https://cn-av.com
[01-Jul-2025 11:16:56 UTC] AUI Debug - Group file #0: ban-chai.png (path: C:\laragon\www\reup/wp-content/uploads/aui-temp/ban-chai_1751368616_0_592.png)
[01-Jul-2025 11:16:56 UTC] AUI Debug - Group file #1: ban-chai_1.png (path: C:\laragon\www\reup/wp-content/uploads/aui-temp/ban-chai_1_1751368616_1_616.png)
[01-Jul-2025 11:16:56 UTC] AUI Debug - Starting upload file #0: ban-chai.png (ID: file_0_1751368616_8424)
[01-Jul-2025 11:16:56 UTC] AUI Debug - File path: C:\laragon\www\reup/wp-content/uploads/aui-temp/ban-chai_1751368616_0_592.png
[01-Jul-2025 11:16:56 UTC] AUI Debug - File URL: http://reup.test/wp-content/uploads/aui-temp/ban-chai_1751368616_0_592.png
[01-Jul-2025 11:16:56 UTC] AUI Debug - upload_single_image called for: ban-chai.png
[01-Jul-2025 11:16:56 UTC] AUI Debug - File path: C:\laragon\www\reup/wp-content/uploads/aui-temp/ban-chai_1751368616_0_592.png
[01-Jul-2025 11:16:56 UTC] AUI Debug - File URL: http://reup.test/wp-content/uploads/aui-temp/ban-chai_1751368616_0_592.png
[01-Jul-2025 11:16:56 UTC] AUI Debug - File identifier: file_0_1751368616_8424
[01-Jul-2025 11:16:56 UTC] AUI Debug - File ban-chai.png (ID: file_0_1751368616_8424) is NEW, proceeding with upload
[01-Jul-2025 11:16:56 UTC] AUI Debug - Current tracker keys: 
[01-Jul-2025 11:16:56 UTC] AUI Debug - Original filename: ban-chai.png
[01-Jul-2025 11:16:56 UTC] AUI Debug - Name without ext: ban-chai
[01-Jul-2025 11:16:56 UTC] AUI Debug - API name: ban-chai
[01-Jul-2025 11:16:56 UTC] AUI Debug - Extension: png
[01-Jul-2025 11:16:56 UTC] AUI Debug - API data prepared: {"upload_auto_delete":0,"type":"image\/png","nameFile":"ban-chai","extFile":"png","linkImage":"http:\/\/reup.test\/wp-content\/uploads\/aui-temp\/ban-chai_1751368616_0_592.png","size":385141}
[01-Jul-2025 11:16:56 UTC] AUI Debug - API Failed for ban-chai.png: HTTP 403: <!DOCTYPE html>
<!--[if lt IE 7]> <html class="no-js ie6 oldie" lang="en-US"> <![endif]-->
<!--[if IE 7]>    <html class="no-js ie7 oldie" lang="en-US"> <![endif]-->
<!--[if IE 8]>    <html class="no-js ie8 oldie" lang="en-US"> <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="en-US"> <!--<![endif]-->
<head>
<title>Attention Required! | Cloudflare</title>
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta http-equiv="X-UA-Compatible" content="IE=Edge" />
<meta name="robots" content="noindex, nofollow" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<link rel="stylesheet" id="cf_styles-css" href="/cdn-cgi/styles/cf.errors.css" />
<!--[if lt IE 9]><link rel="stylesheet" id='cf_styles-ie-css' href="/cdn-cgi/styles/cf.errors.ie.css" /><![endif]-->
<style>body{margin:0;padding:0}</style>


<!--[if gte IE 10]><!-->
<script>
  if (!navigator.cookieEnabled) {
    window.addEventListener('DOMContentLoaded', function () {
      var cookieEl = document.getElementById('cookie-alert');
      cookieEl.style.display = 'block';
    })
  }
</script>
<!--<![endif]-->

</head>
<body>
  <div id="cf-wrapper">
    <div class="cf-alert cf-alert-error cf-cookie-error" id="cookie-alert" data-translate="enable_cookies">Please enable cookies.</div>
    <div id="cf-error-details" class="cf-error-details-wrapper">
      <div class="cf-wrapper cf-header cf-error-overview">
        <h1 data-translate="block_headline">Sorry, you have been blocked</h1>
        <h2 class="cf-subheadline"><span data-translate="unable_to_access">You are unable to access</span> cn-av.com</h2>
      </div><!-- /.header -->

      <div class="cf-section cf-highlight">
        <div class="cf-wrapper">
          <div class="cf-screenshot-container cf-screenshot-full">
            
              <span class="cf-no-screenshot error"></span>
            
          </div>
        </div>
      </div><!-- /.captcha-container -->

      <div class="cf-section cf-wrapper">
        <div class="cf-columns two">
          <div class="cf-column">
            <h2 data-translate="blocked_why_headline">Why have I been blocked?</h2>

            <p data-translate="blocked_why_detail">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.</p>
          </div>

          <div class="cf-column">
            <h2 data-translate="blocked_resolve_headline">What can I do to resolve this?</h2>

            <p data-translate="blocked_resolve_detail">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.</p>
          </div>
        </div>
      </div><!-- /.section -->

      <div class="cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300">
    <p class="text-13">
      <span class="cf-footer-item sm:block sm:mb-1">Cloudflare Ray ID: <strong class="font-semibold">95857e8a4dde7161</strong></span>
      <span class="cf-footer-separator sm:hidden">&bull;</span>
      <span id="cf-footer-item-ip" class="cf-footer-item hidden sm:block sm:mb-1">
        Your IP:
        <button type="button" id="cf-footer-ip-reveal" class="cf-footer-ip-reveal-btn">Click to reveal</button>
        <span class="hidden" id="cf-footer-ip">*************</span>
        <span class="cf-footer-separator sm:hidden">&bull;</span>
      </span>
      <span class="cf-footer-item sm:block sm:mb-1"><span>Performance &amp; security by</span> <a rel="noopener noreferrer" href="https://www.cloudflare.com/5xx-error-landing" id="brand_link" target="_blank">Cloudflare</a></span>
      
    </p>
    <script>(function(){function d(){var b=a.getElementById("cf-footer-item-ip"),c=a.getElementById("cf-footer-ip-reveal");b&&"classList"in b&&(b.classList.remove("hidden"),c.addEventListener("click",function(){c.classList.add("hidden");a.getElementById("cf-footer-ip").classList.remove("hidden")}))}var a=document;document.addEventListener&&a.addEventListener("DOMContentLoaded",d)})();</script>
  </div><!-- /.error-footer -->

    </div><!-- /#cf-error-details -->
  </div><!-- /#cf-wrapper -->

  <script>
    window._cf_translation = {};
    
    
  </script>
</body>
</html>
[01-Jul-2025 11:16:57 UTC] AUI Debug - API Failed for ban-chai.png: HTTP 403: <!DOCTYPE html>
<!--[if lt IE 7]> <html class="no-js ie6 oldie" lang="en-US"> <![endif]-->
<!--[if IE 7]>    <html class="no-js ie7 oldie" lang="en-US"> <![endif]-->
<!--[if IE 8]>    <html class="no-js ie8 oldie" lang="en-US"> <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="en-US"> <!--<![endif]-->
<head>
<title>Attention Required! | Cloudflare</title>
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta http-equiv="X-UA-Compatible" content="IE=Edge" />
<meta name="robots" content="noindex, nofollow" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<link rel="stylesheet" id="cf_styles-css" href="/cdn-cgi/styles/cf.errors.css" />
<!--[if lt IE 9]><link rel="stylesheet" id='cf_styles-ie-css' href="/cdn-cgi/styles/cf.errors.ie.css" /><![endif]-->
<style>body{margin:0;padding:0}</style>


<!--[if gte IE 10]><!-->
<script>
  if (!navigator.cookieEnabled) {
    window.addEventListener('DOMContentLoaded', function () {
      var cookieEl = document.getElementById('cookie-alert');
      cookieEl.style.display = 'block';
    })
  }
</script>
<!--<![endif]-->

</head>
<body>
  <div id="cf-wrapper">
    <div class="cf-alert cf-alert-error cf-cookie-error" id="cookie-alert" data-translate="enable_cookies">Please enable cookies.</div>
    <div id="cf-error-details" class="cf-error-details-wrapper">
      <div class="cf-wrapper cf-header cf-error-overview">
        <h1 data-translate="block_headline">Sorry, you have been blocked</h1>
        <h2 class="cf-subheadline"><span data-translate="unable_to_access">You are unable to access</span> cn-av.com</h2>
      </div><!-- /.header -->

      <div class="cf-section cf-highlight">
        <div class="cf-wrapper">
          <div class="cf-screenshot-container cf-screenshot-full">
            
              <span class="cf-no-screenshot error"></span>
            
          </div>
        </div>
      </div><!-- /.captcha-container -->

      <div class="cf-section cf-wrapper">
        <div class="cf-columns two">
          <div class="cf-column">
            <h2 data-translate="blocked_why_headline">Why have I been blocked?</h2>

            <p data-translate="blocked_why_detail">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.</p>
          </div>

          <div class="cf-column">
            <h2 data-translate="blocked_resolve_headline">What can I do to resolve this?</h2>

            <p data-translate="blocked_resolve_detail">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.</p>
          </div>
        </div>
      </div><!-- /.section -->

      <div class="cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300">
    <p class="text-13">
      <span class="cf-footer-item sm:block sm:mb-1">Cloudflare Ray ID: <strong class="font-semibold">95857e915f2909d8</strong></span>
      <span class="cf-footer-separator sm:hidden">&bull;</span>
      <span id="cf-footer-item-ip" class="cf-footer-item hidden sm:block sm:mb-1">
        Your IP:
        <button type="button" id="cf-footer-ip-reveal" class="cf-footer-ip-reveal-btn">Click to reveal</button>
        <span class="hidden" id="cf-footer-ip">*************</span>
        <span class="cf-footer-separator sm:hidden">&bull;</span>
      </span>
      <span class="cf-footer-item sm:block sm:mb-1"><span>Performance &amp; security by</span> <a rel="noopener noreferrer" href="https://www.cloudflare.com/5xx-error-landing" id="brand_link" target="_blank">Cloudflare</a></span>
      
    </p>
    <script>(function(){function d(){var b=a.getElementById("cf-footer-item-ip"),c=a.getElementById("cf-footer-ip-reveal");b&&"classList"in b&&(b.classList.remove("hidden"),c.addEventListener("click",function(){c.classList.add("hidden");a.getElementById("cf-footer-ip").classList.remove("hidden")}))}var a=document;document.addEventListener&&a.addEventListener("DOMContentLoaded",d)})();</script>
  </div><!-- /.error-footer -->

    </div><!-- /#cf-error-details -->
  </div><!-- /#cf-wrapper -->

  <script>
    window._cf_translation = {};
    
    
  </script>
</body>
</html>
[01-Jul-2025 11:16:58 UTC] AUI Debug - API Failed for ban-chai.png: HTTP 403: <!DOCTYPE html>
<!--[if lt IE 7]> <html class="no-js ie6 oldie" lang="en-US"> <![endif]-->
<!--[if IE 7]>    <html class="no-js ie7 oldie" lang="en-US"> <![endif]-->
<!--[if IE 8]>    <html class="no-js ie8 oldie" lang="en-US"> <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="en-US"> <!--<![endif]-->
<head>
<title>Attention Required! | Cloudflare</title>
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta http-equiv="X-UA-Compatible" content="IE=Edge" />
<meta name="robots" content="noindex, nofollow" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<link rel="stylesheet" id="cf_styles-css" href="/cdn-cgi/styles/cf.errors.css" />
<!--[if lt IE 9]><link rel="stylesheet" id='cf_styles-ie-css' href="/cdn-cgi/styles/cf.errors.ie.css" /><![endif]-->
<style>body{margin:0;padding:0}</style>


<!--[if gte IE 10]><!-->
<script>
  if (!navigator.cookieEnabled) {
    window.addEventListener('DOMContentLoaded', function () {
      var cookieEl = document.getElementById('cookie-alert');
      cookieEl.style.display = 'block';
    })
  }
</script>
<!--<![endif]-->

</head>
<body>
  <div id="cf-wrapper">
    <div class="cf-alert cf-alert-error cf-cookie-error" id="cookie-alert" data-translate="enable_cookies">Please enable cookies.</div>
    <div id="cf-error-details" class="cf-error-details-wrapper">
      <div class="cf-wrapper cf-header cf-error-overview">
        <h1 data-translate="block_headline">Sorry, you have been blocked</h1>
        <h2 class="cf-subheadline"><span data-translate="unable_to_access">You are unable to access</span> cn-av.com</h2>
      </div><!-- /.header -->

      <div class="cf-section cf-highlight">
        <div class="cf-wrapper">
          <div class="cf-screenshot-container cf-screenshot-full">
            
              <span class="cf-no-screenshot error"></span>
            
          </div>
        </div>
      </div><!-- /.captcha-container -->

      <div class="cf-section cf-wrapper">
        <div class="cf-columns two">
          <div class="cf-column">
            <h2 data-translate="blocked_why_headline">Why have I been blocked?</h2>

            <p data-translate="blocked_why_detail">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.</p>
          </div>

          <div class="cf-column">
            <h2 data-translate="blocked_resolve_headline">What can I do to resolve this?</h2>

            <p data-translate="blocked_resolve_detail">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.</p>
          </div>
        </div>
      </div><!-- /.section -->

      <div class="cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300">
    <p class="text-13">
      <span class="cf-footer-item sm:block sm:mb-1">Cloudflare Ray ID: <strong class="font-semibold">95857e983bf6848e</strong></span>
      <span class="cf-footer-separator sm:hidden">&bull;</span>
      <span id="cf-footer-item-ip" class="cf-footer-item hidden sm:block sm:mb-1">
        Your IP:
        <button type="button" id="cf-footer-ip-reveal" class="cf-footer-ip-reveal-btn">Click to reveal</button>
        <span class="hidden" id="cf-footer-ip">*************</span>
        <span class="cf-footer-separator sm:hidden">&bull;</span>
      </span>
      <span class="cf-footer-item sm:block sm:mb-1"><span>Performance &amp; security by</span> <a rel="noopener noreferrer" href="https://www.cloudflare.com/5xx-error-landing" id="brand_link" target="_blank">Cloudflare</a></span>
      
    </p>
    <script>(function(){function d(){var b=a.getElementById("cf-footer-item-ip"),c=a.getElementById("cf-footer-ip-reveal");b&&"classList"in b&&(b.classList.remove("hidden"),c.addEventListener("click",function(){c.classList.add("hidden");a.getElementById("cf-footer-ip").classList.remove("hidden")}))}var a=document;document.addEventListener&&a.addEventListener("DOMContentLoaded",d)})();</script>
  </div><!-- /.error-footer -->

    </div><!-- /#cf-error-details -->
  </div><!-- /#cf-wrapper -->

  <script>
    window._cf_translation = {};
    
    
  </script>
</body>
</html>
[01-Jul-2025 11:16:58 UTC] AUI Debug - Upload completed for ban-chai.png: FAILED
[01-Jul-2025 11:16:58 UTC] AUI Debug - Starting upload file #1: ban-chai_1.png (ID: file_1_1751368616_6362)
[01-Jul-2025 11:16:58 UTC] AUI Debug - File path: C:\laragon\www\reup/wp-content/uploads/aui-temp/ban-chai_1_1751368616_1_616.png
[01-Jul-2025 11:16:58 UTC] AUI Debug - File URL: http://reup.test/wp-content/uploads/aui-temp/ban-chai_1_1751368616_1_616.png
[01-Jul-2025 11:16:59 UTC] AUI Debug - upload_single_image called for: ban-chai_1.png
[01-Jul-2025 11:16:59 UTC] AUI Debug - File path: C:\laragon\www\reup/wp-content/uploads/aui-temp/ban-chai_1_1751368616_1_616.png
[01-Jul-2025 11:16:59 UTC] AUI Debug - File URL: http://reup.test/wp-content/uploads/aui-temp/ban-chai_1_1751368616_1_616.png
[01-Jul-2025 11:16:59 UTC] AUI Debug - File identifier: file_1_1751368616_6362
[01-Jul-2025 11:16:59 UTC] AUI Debug - File ban-chai_1.png (ID: file_1_1751368616_6362) is NEW, proceeding with upload
[01-Jul-2025 11:16:59 UTC] AUI Debug - Current tracker keys: file_0_1751368616_8424
[01-Jul-2025 11:16:59 UTC] AUI Debug - Original filename: ban-chai_1.png
[01-Jul-2025 11:16:59 UTC] AUI Debug - Name without ext: ban-chai_1
[01-Jul-2025 11:16:59 UTC] AUI Debug - API name: ban-chai_1
[01-Jul-2025 11:16:59 UTC] AUI Debug - Extension: png
[01-Jul-2025 11:16:59 UTC] AUI Debug - API data prepared: {"upload_auto_delete":0,"type":"image\/png","nameFile":"ban-chai_1","extFile":"png","linkImage":"http:\/\/reup.test\/wp-content\/uploads\/aui-temp\/ban-chai_1_1751368616_1_616.png","size":598799}
[01-Jul-2025 11:16:59 UTC] AUI Debug - API Failed for ban-chai_1.png: HTTP 403: <!DOCTYPE html>
<!--[if lt IE 7]> <html class="no-js ie6 oldie" lang="en-US"> <![endif]-->
<!--[if IE 7]>    <html class="no-js ie7 oldie" lang="en-US"> <![endif]-->
<!--[if IE 8]>    <html class="no-js ie8 oldie" lang="en-US"> <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="en-US"> <!--<![endif]-->
<head>
<title>Attention Required! | Cloudflare</title>
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta http-equiv="X-UA-Compatible" content="IE=Edge" />
<meta name="robots" content="noindex, nofollow" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<link rel="stylesheet" id="cf_styles-css" href="/cdn-cgi/styles/cf.errors.css" />
<!--[if lt IE 9]><link rel="stylesheet" id='cf_styles-ie-css' href="/cdn-cgi/styles/cf.errors.ie.css" /><![endif]-->
<style>body{margin:0;padding:0}</style>


<!--[if gte IE 10]><!-->
<script>
  if (!navigator.cookieEnabled) {
    window.addEventListener('DOMContentLoaded', function () {
      var cookieEl = document.getElementById('cookie-alert');
      cookieEl.style.display = 'block';
    })
  }
</script>
<!--<![endif]-->

</head>
<body>
  <div id="cf-wrapper">
    <div class="cf-alert cf-alert-error cf-cookie-error" id="cookie-alert" data-translate="enable_cookies">Please enable cookies.</div>
    <div id="cf-error-details" class="cf-error-details-wrapper">
      <div class="cf-wrapper cf-header cf-error-overview">
        <h1 data-translate="block_headline">Sorry, you have been blocked</h1>
        <h2 class="cf-subheadline"><span data-translate="unable_to_access">You are unable to access</span> cn-av.com</h2>
      </div><!-- /.header -->

      <div class="cf-section cf-highlight">
        <div class="cf-wrapper">
          <div class="cf-screenshot-container cf-screenshot-full">
            
              <span class="cf-no-screenshot error"></span>
            
          </div>
        </div>
      </div><!-- /.captcha-container -->

      <div class="cf-section cf-wrapper">
        <div class="cf-columns two">
          <div class="cf-column">
            <h2 data-translate="blocked_why_headline">Why have I been blocked?</h2>

            <p data-translate="blocked_why_detail">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.</p>
          </div>

          <div class="cf-column">
            <h2 data-translate="blocked_resolve_headline">What can I do to resolve this?</h2>

            <p data-translate="blocked_resolve_detail">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.</p>
          </div>
        </div>
      </div><!-- /.section -->

      <div class="cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300">
    <p class="text-13">
      <span class="cf-footer-item sm:block sm:mb-1">Cloudflare Ray ID: <strong class="font-semibold">95857e9f48ee853a</strong></span>
      <span class="cf-footer-separator sm:hidden">&bull;</span>
      <span id="cf-footer-item-ip" class="cf-footer-item hidden sm:block sm:mb-1">
        Your IP:
        <button type="button" id="cf-footer-ip-reveal" class="cf-footer-ip-reveal-btn">Click to reveal</button>
        <span class="hidden" id="cf-footer-ip">*************</span>
        <span class="cf-footer-separator sm:hidden">&bull;</span>
      </span>
      <span class="cf-footer-item sm:block sm:mb-1"><span>Performance &amp; security by</span> <a rel="noopener noreferrer" href="https://www.cloudflare.com/5xx-error-landing" id="brand_link" target="_blank">Cloudflare</a></span>
      
    </p>
    <script>(function(){function d(){var b=a.getElementById("cf-footer-item-ip"),c=a.getElementById("cf-footer-ip-reveal");b&&"classList"in b&&(b.classList.remove("hidden"),c.addEventListener("click",function(){c.classList.add("hidden");a.getElementById("cf-footer-ip").classList.remove("hidden")}))}var a=document;document.addEventListener&&a.addEventListener("DOMContentLoaded",d)})();</script>
  </div><!-- /.error-footer -->

    </div><!-- /#cf-error-details -->
  </div><!-- /#cf-wrapper -->

  <script>
    window._cf_translation = {};
    
    
  </script>
</body>
</html>
[01-Jul-2025 11:17:00 UTC] AUI Debug - API Failed for ban-chai_1.png: HTTP 403: <!DOCTYPE html>
<!--[if lt IE 7]> <html class="no-js ie6 oldie" lang="en-US"> <![endif]-->
<!--[if IE 7]>    <html class="no-js ie7 oldie" lang="en-US"> <![endif]-->
<!--[if IE 8]>    <html class="no-js ie8 oldie" lang="en-US"> <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="en-US"> <!--<![endif]-->
<head>
<title>Attention Required! | Cloudflare</title>
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta http-equiv="X-UA-Compatible" content="IE=Edge" />
<meta name="robots" content="noindex, nofollow" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<link rel="stylesheet" id="cf_styles-css" href="/cdn-cgi/styles/cf.errors.css" />
<!--[if lt IE 9]><link rel="stylesheet" id='cf_styles-ie-css' href="/cdn-cgi/styles/cf.errors.ie.css" /><![endif]-->
<style>body{margin:0;padding:0}</style>


<!--[if gte IE 10]><!-->
<script>
  if (!navigator.cookieEnabled) {
    window.addEventListener('DOMContentLoaded', function () {
      var cookieEl = document.getElementById('cookie-alert');
      cookieEl.style.display = 'block';
    })
  }
</script>
<!--<![endif]-->

</head>
<body>
  <div id="cf-wrapper">
    <div class="cf-alert cf-alert-error cf-cookie-error" id="cookie-alert" data-translate="enable_cookies">Please enable cookies.</div>
    <div id="cf-error-details" class="cf-error-details-wrapper">
      <div class="cf-wrapper cf-header cf-error-overview">
        <h1 data-translate="block_headline">Sorry, you have been blocked</h1>
        <h2 class="cf-subheadline"><span data-translate="unable_to_access">You are unable to access</span> cn-av.com</h2>
      </div><!-- /.header -->

      <div class="cf-section cf-highlight">
        <div class="cf-wrapper">
          <div class="cf-screenshot-container cf-screenshot-full">
            
              <span class="cf-no-screenshot error"></span>
            
          </div>
        </div>
      </div><!-- /.captcha-container -->

      <div class="cf-section cf-wrapper">
        <div class="cf-columns two">
          <div class="cf-column">
            <h2 data-translate="blocked_why_headline">Why have I been blocked?</h2>

            <p data-translate="blocked_why_detail">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.</p>
          </div>

          <div class="cf-column">
            <h2 data-translate="blocked_resolve_headline">What can I do to resolve this?</h2>

            <p data-translate="blocked_resolve_detail">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.</p>
          </div>
        </div>
      </div><!-- /.section -->

      <div class="cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300">
    <p class="text-13">
      <span class="cf-footer-item sm:block sm:mb-1">Cloudflare Ray ID: <strong class="font-semibold">95857ea64c12dd59</strong></span>
      <span class="cf-footer-separator sm:hidden">&bull;</span>
      <span id="cf-footer-item-ip" class="cf-footer-item hidden sm:block sm:mb-1">
        Your IP:
        <button type="button" id="cf-footer-ip-reveal" class="cf-footer-ip-reveal-btn">Click to reveal</button>
        <span class="hidden" id="cf-footer-ip">*************</span>
        <span class="cf-footer-separator sm:hidden">&bull;</span>
      </span>
      <span class="cf-footer-item sm:block sm:mb-1"><span>Performance &amp; security by</span> <a rel="noopener noreferrer" href="https://www.cloudflare.com/5xx-error-landing" id="brand_link" target="_blank">Cloudflare</a></span>
      
    </p>
    <script>(function(){function d(){var b=a.getElementById("cf-footer-item-ip"),c=a.getElementById("cf-footer-ip-reveal");b&&"classList"in b&&(b.classList.remove("hidden"),c.addEventListener("click",function(){c.classList.add("hidden");a.getElementById("cf-footer-ip").classList.remove("hidden")}))}var a=document;document.addEventListener&&a.addEventListener("DOMContentLoaded",d)})();</script>
  </div><!-- /.error-footer -->

    </div><!-- /#cf-error-details -->
  </div><!-- /#cf-wrapper -->

  <script>
    window._cf_translation = {};
    
    
  </script>
</body>
</html>
[01-Jul-2025 11:17:02 UTC] AUI Debug - API Failed for ban-chai_1.png: HTTP 403: <!DOCTYPE html>
<!--[if lt IE 7]> <html class="no-js ie6 oldie" lang="en-US"> <![endif]-->
<!--[if IE 7]>    <html class="no-js ie7 oldie" lang="en-US"> <![endif]-->
<!--[if IE 8]>    <html class="no-js ie8 oldie" lang="en-US"> <![endif]-->
<!--[if gt IE 8]><!--> <html class="no-js" lang="en-US"> <!--<![endif]-->
<head>
<title>Attention Required! | Cloudflare</title>
<meta charset="UTF-8" />
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8" />
<meta http-equiv="X-UA-Compatible" content="IE=Edge" />
<meta name="robots" content="noindex, nofollow" />
<meta name="viewport" content="width=device-width,initial-scale=1" />
<link rel="stylesheet" id="cf_styles-css" href="/cdn-cgi/styles/cf.errors.css" />
<!--[if lt IE 9]><link rel="stylesheet" id='cf_styles-ie-css' href="/cdn-cgi/styles/cf.errors.ie.css" /><![endif]-->
<style>body{margin:0;padding:0}</style>


<!--[if gte IE 10]><!-->
<script>
  if (!navigator.cookieEnabled) {
    window.addEventListener('DOMContentLoaded', function () {
      var cookieEl = document.getElementById('cookie-alert');
      cookieEl.style.display = 'block';
    })
  }
</script>
<!--<![endif]-->

</head>
<body>
  <div id="cf-wrapper">
    <div class="cf-alert cf-alert-error cf-cookie-error" id="cookie-alert" data-translate="enable_cookies">Please enable cookies.</div>
    <div id="cf-error-details" class="cf-error-details-wrapper">
      <div class="cf-wrapper cf-header cf-error-overview">
        <h1 data-translate="block_headline">Sorry, you have been blocked</h1>
        <h2 class="cf-subheadline"><span data-translate="unable_to_access">You are unable to access</span> cn-av.com</h2>
      </div><!-- /.header -->

      <div class="cf-section cf-highlight">
        <div class="cf-wrapper">
          <div class="cf-screenshot-container cf-screenshot-full">
            
              <span class="cf-no-screenshot error"></span>
            
          </div>
        </div>
      </div><!-- /.captcha-container -->

      <div class="cf-section cf-wrapper">
        <div class="cf-columns two">
          <div class="cf-column">
            <h2 data-translate="blocked_why_headline">Why have I been blocked?</h2>

            <p data-translate="blocked_why_detail">This website is using a security service to protect itself from online attacks. The action you just performed triggered the security solution. There are several actions that could trigger this block including submitting a certain word or phrase, a SQL command or malformed data.</p>
          </div>

          <div class="cf-column">
            <h2 data-translate="blocked_resolve_headline">What can I do to resolve this?</h2>

            <p data-translate="blocked_resolve_detail">You can email the site owner to let them know you were blocked. Please include what you were doing when this page came up and the Cloudflare Ray ID found at the bottom of this page.</p>
          </div>
        </div>
      </div><!-- /.section -->

      <div class="cf-error-footer cf-wrapper w-240 lg:w-full py-10 sm:py-4 sm:px-8 mx-auto text-center sm:text-left border-solid border-0 border-t border-gray-300">
    <p class="text-13">
      <span class="cf-footer-item sm:block sm:mb-1">Cloudflare Ray ID: <strong class="font-semibold">95857ead2e1885e6</strong></span>
      <span class="cf-footer-separator sm:hidden">&bull;</span>
      <span id="cf-footer-item-ip" class="cf-footer-item hidden sm:block sm:mb-1">
        Your IP:
        <button type="button" id="cf-footer-ip-reveal" class="cf-footer-ip-reveal-btn">Click to reveal</button>
        <span class="hidden" id="cf-footer-ip">*************</span>
        <span class="cf-footer-separator sm:hidden">&bull;</span>
      </span>
      <span class="cf-footer-item sm:block sm:mb-1"><span>Performance &amp; security by</span> <a rel="noopener noreferrer" href="https://www.cloudflare.com/5xx-error-landing" id="brand_link" target="_blank">Cloudflare</a></span>
      
    </p>
    <script>(function(){function d(){var b=a.getElementById("cf-footer-item-ip"),c=a.getElementById("cf-footer-ip-reveal");b&&"classList"in b&&(b.classList.remove("hidden"),c.addEventListener("click",function(){c.classList.add("hidden");a.getElementById("cf-footer-ip").classList.remove("hidden")}))}var a=document;document.addEventListener&&a.addEventListener("DOMContentLoaded",d)})();</script>
  </div><!-- /.error-footer -->

    </div><!-- /#cf-error-details -->
  </div><!-- /#cf-wrapper -->

  <script>
    window._cf_translation = {};
    
    
  </script>
</body>
</html>
[01-Jul-2025 11:17:02 UTC] AUI Debug - Upload completed for ban-chai_1.png: FAILED
[01-Jul-2025 11:17:02 UTC] AUI Debug - Group upload completed. Total results: 2
[01-Jul-2025 11:17:02 UTC] AUI Debug - Group result #0: ban-chai.png (ID: file_0_1751368616_8424) - FAILED
[01-Jul-2025 11:17:02 UTC] AUI Debug - Group result #1: ban-chai_1.png (ID: file_1_1751368616_6362) - FAILED
[01-Jul-2025 11:17:02 UTC] AUI Debug - Group 0 upload completed, got 2 results
[01-Jul-2025 11:17:02 UTC] AUI Debug - Result for ban-chai.png (ID: file_0_1751368616_8424): FAILED
[01-Jul-2025 11:17:02 UTC] AUI Debug - Result for ban-chai_1.png (ID: file_1_1751368616_6362): FAILED
[01-Jul-2025 11:17:02 UTC] AUI Debug - Added result for ban-chai.png (ID: file_0_1751368616_8424)
[01-Jul-2025 11:17:02 UTC] AUI Debug - DUPLICATE DETECTED! Skipping result for ban-chai.png (ID: file_0_1751368616_8424)
[01-Jul-2025 11:17:02 UTC] AUI Debug - Total results: 1
[01-Jul-2025 11:17:02 UTC] AUI Debug - Successful: 0
[01-Jul-2025 11:17:02 UTC] AUI Debug - Failed: 1
[01-Jul-2025 11:17:02 UTC] AUI Debug - Final results being sent to client:
[01-Jul-2025 11:17:02 UTC] AUI Debug - Final result #0: ban-chai.png (ID: file_0_1751368616_8424)
[01-Jul-2025 11:25:32 UTC] PHP Fatal error:  Uncaught Error: Call to undefined function dd() in C:\laragon\www\reup\wp-content\plugins\auto-upload-image\includes\class-image-uploader.php:172
Stack trace:
#0 C:\laragon\www\reup\wp-content\plugins\auto-upload-image\includes\class-image-uploader.php(49): AUI_Image_Uploader->process_uploaded_files(Array)
#1 C:\laragon\www\reup\wp-includes\class-wp-hook.php(324): AUI_Image_Uploader->ajax_upload_images('')
#2 C:\laragon\www\reup\wp-includes\class-wp-hook.php(348): WP_Hook->apply_filters('', Array)
#3 C:\laragon\www\reup\wp-includes\plugin.php(517): WP_Hook->do_action(Array)
#4 C:\laragon\www\reup\wp-admin\admin-ajax.php(192): do_action('wp_ajax_aui_upl...')
#5 {main}
  thrown in C:\laragon\www\reup\wp-content\plugins\auto-upload-image\includes\class-image-uploader.php on line 172
