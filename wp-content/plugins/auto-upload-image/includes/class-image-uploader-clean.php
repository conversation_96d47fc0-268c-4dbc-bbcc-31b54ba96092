<?php

/**
 * Image Uploader Class - Clean Version (No Debug Logs)
 */

if (!defined('ABSPATH')) {
    exit;
}

class AUI_Image_Uploader {
    
    private $config_manager;
    private $upload_results = array();
    private $current_domain_index = 0;
    private $uploaded_files_tracker = array();
    
    /**
     * Constructor
     */
    public function __construct($config_manager) {
        $this->config_manager = $config_manager;
        
        // Register AJAX handlers
        add_action('wp_ajax_aui_upload_images', array($this, 'handle_ajax_upload'));
        add_action('wp_ajax_aui_delete_uploaded_files', array($this, 'handle_ajax_delete'));
    }
    
    /**
     * Handle AJAX upload request
     */
    public function handle_ajax_upload() {
        // Verify nonce
        if (!wp_verify_nonce($_POST['nonce'], 'aui_nonce')) {
            wp_die('Security check failed');
        }
        
        // Check user capabilities
        if (!current_user_can('upload_files')) {
            wp_die('Insufficient permissions');
        }
        
        // Check if files were uploaded
        if (empty($_FILES['images']['name'][0])) {
            wp_send_json_error('No files uploaded');
        }
        
        $results = $this->process_uploaded_files($_FILES['images']);
        
        if (!empty($results['results'])) {
            wp_send_json_success(array(
                'message' => 'Upload completed successfully!',
                'results' => $results['results'],
                'successful_uploads' => $results['successful_uploads'],
                'failed_uploads' => $results['failed_uploads']
            ));
        } else {
            wp_send_json_error('No files were processed');
        }
    }
    
    /**
     * Handle AJAX delete request
     */
    public function handle_ajax_delete() {
        if (!wp_verify_nonce($_POST['nonce'], 'aui_nonce')) {
            wp_die('Security check failed');
        }
        
        if (!current_user_can('upload_files')) {
            wp_die('Insufficient permissions');
        }
        
        $file_paths = isset($_POST['file_paths']) ? $_POST['file_paths'] : array();
        $deleted_count = 0;
        
        foreach ($file_paths as $file_path) {
            if (file_exists($file_path) && strpos($file_path, 'aui-temp') !== false) {
                if (unlink($file_path)) {
                    $deleted_count++;
                }
            }
        }
        
        wp_send_json_success(array(
            'message' => "Deleted $deleted_count files",
            'deleted_count' => $deleted_count
        ));
    }
    
    /**
     * Process uploaded files
     */
    public function process_uploaded_files($files) {
        $file_count = count($files['name']);
        $uploaded_files = array();
        $processed_files = array();
        
        // Create temp directory if not exists
        $upload_dir = wp_upload_dir();
        $temp_dir = $upload_dir['basedir'] . '/aui-temp';
        if (!file_exists($temp_dir)) {
            wp_mkdir_p($temp_dir);
        }
        
        // Process each uploaded file
        for ($i = 0; $i < $file_count; $i++) {
            if ($files['error'][$i] !== UPLOAD_ERR_OK) {
                continue;
            }
            
            $original_filename = sanitize_file_name($files['name'][$i]);
            $temp_file = $files['tmp_name'][$i];
            $file_size = $files['size'][$i];
            
            // Skip empty files
            if ($file_size == 0) {
                continue;
            }
            
            // Create unique key for duplicate detection
            $file_key = md5($original_filename . $file_size);
            
            // Skip duplicates
            if (isset($processed_files[$file_key])) {
                continue;
            }
            $processed_files[$file_key] = true;
            
            // Validate file type
            $file_type = wp_check_filetype($original_filename);
            if (!in_array($file_type['type'], array('image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'))) {
                continue;
            }
            
            // Generate unique filename for temp storage
            $filename = pathinfo($original_filename, PATHINFO_FILENAME);
            $extension = pathinfo($original_filename, PATHINFO_EXTENSION);
            $unique_filename = $filename . '_' . time() . '_' . $i . '_' . rand(100, 999) . '.' . $extension;
            $temp_path = $temp_dir . '/' . $unique_filename;
            
            if (move_uploaded_file($temp_file, $temp_path)) {
                // Create unique ID for this file
                $file_unique_id = 'file_' . $i . '_' . time() . '_' . rand(1000, 9999);
                
                $uploaded_files[] = array(
                    'unique_id' => $file_unique_id,
                    'filename' => $original_filename,
                    'temp_filename' => $unique_filename,
                    'path' => $temp_path,
                    'url' => $upload_dir['baseurl'] . '/aui-temp/' . $unique_filename,
                    'size' => filesize($temp_path)
                );
            }
        }
        
        if (empty($uploaded_files)) {
            return array('results' => array(), 'successful_uploads' => array(), 'failed_uploads' => array());
        }
        
        // Upload files
        return $this->upload_multiple_images($uploaded_files);
    }
    
    /**
     * Upload multiple images with grouping
     */
    public function upload_multiple_images($image_files) {
        $this->upload_results = array();
        $this->uploaded_files_tracker = array();
        $domains = $this->config_manager->get_domains();
        
        if (empty($domains)) {
            return array('results' => array(), 'successful_uploads' => array(), 'failed_uploads' => array());
        }
        
        // Group images by similar names
        $grouped_images = $this->group_images_by_name($image_files);
        
        $all_results = array();
        $successful_uploads = array();
        $failed_uploads = array();
        $processed_unique_ids = array();
        
        // Upload each group
        foreach ($grouped_images as $group_index => $group) {
            $group_results = $this->upload_image_group($group, $domains);
            
            // Add results with duplicate prevention
            foreach ($group_results as $result) {
                $unique_id = isset($result['unique_id']) ? $result['unique_id'] : 'no_id_' . uniqid();
                
                if (!in_array($unique_id, $processed_unique_ids)) {
                    $processed_unique_ids[] = $unique_id;
                    $all_results[] = $result;
                    
                    if ($result['success']) {
                        $successful_uploads[] = $result;
                    } else {
                        $failed_uploads[] = $result;
                    }
                }
            }
        }
        
        return array(
            'results' => $all_results,
            'successful_uploads' => $successful_uploads,
            'failed_uploads' => $failed_uploads
        );
    }
    
    /**
     * Sort files by name for better grouping
     */
    private function sort_files_by_name($image_files) {
        usort($image_files, function($a, $b) {
            return strcmp($a['filename'], $b['filename']);
        });
        return $image_files;
    }
    
    /**
     * Group images by similar base names
     */
    private function group_images_by_name($image_files) {
        // Sort files first
        $image_files = $this->sort_files_by_name($image_files);
        
        $groups = array();
        $current_group = array();
        $current_base = '';
        
        foreach ($image_files as $file) {
            $filename = pathinfo($file['filename'], PATHINFO_FILENAME);
            $file_id = isset($file['unique_id']) ? $file['unique_id'] : 'no_id';
            $base_name = $this->extract_base_name($filename);
            
            if ($current_base === '' || $current_base === $base_name) {
                $current_group[] = $file;
                $current_base = $base_name;
            } else {
                if (!empty($current_group)) {
                    $groups[] = $current_group;
                }
                $current_group = array($file);
                $current_base = $base_name;
            }
        }
        
        if (!empty($current_group)) {
            $groups[] = $current_group;
        }
        
        // Sort files within each group by number
        foreach ($groups as $group_index => &$group) {
            usort($group, function($a, $b) {
                $num_a = $this->extract_number_from_filename(pathinfo($a['filename'], PATHINFO_FILENAME));
                $num_b = $this->extract_number_from_filename(pathinfo($b['filename'], PATHINFO_FILENAME));
                return $num_a - $num_b;
            });
        }
        
        return $groups;
    }
    
    /**
     * Extract base name from filename
     */
    private function extract_base_name($name_without_ext) {
        // Pattern: name_number (e.g., ban-chai_1, ban-chai_2)
        if (preg_match('/^(.+)_(\d+)$/', $name_without_ext, $matches)) {
            $base = $matches[1];
            $number = $matches[2];
            return $base;
        }
        
        return $name_without_ext;
    }
    
    /**
     * Extract number from filename for sorting
     */
    private function extract_number_from_filename($name_without_ext) {
        // Look for simple number pattern: name_number
        if (preg_match('/_(\d+)$/', $name_without_ext, $matches)) {
            $number = intval($matches[1]);
            return $number;
        }
        
        return 0;
    }
    
    /**
     * Upload a group of images to the same domain
     */
    private function upload_image_group($image_group, $domains) {
        $domain = $this->get_next_domain($domains);
        $results = array();
        $delay_seconds = $this->config_manager->get_setting('delay_seconds', 1);
        
        foreach ($image_group as $index => $file) {
            // Add delay between uploads to same domain (except first image)
            if ($index > 0 && count($image_group) > 1) {
                sleep($delay_seconds);
            }
            
            $result = $this->upload_single_image($file, $domain);
            
            if ($result !== null) {
                $results[] = $result;
            }
        }
        
        return $results;
    }
    
    /**
     * Get next domain using round-robin
     */
    private function get_next_domain($domains) {
        $domain = $domains[$this->current_domain_index % count($domains)];
        $this->current_domain_index++;
        return $domain;
    }

    /**
     * Upload single image with retry logic
     */
    private function upload_single_image($file, $domain) {
        $retry_attempts = $this->config_manager->get_setting('retry_attempts', 2);
        $filename = $file['filename'];
        $file_path = $file['path'];
        $file_url = $file['url'];

        // Use unique_id as file identifier to prevent duplicates
        $file_identifier = isset($file['unique_id']) ? $file['unique_id'] : md5($filename . $file_path . microtime(true));

        // Check if this file has already been uploaded
        if (isset($this->uploaded_files_tracker[$file_identifier])) {
            return $this->uploaded_files_tracker[$file_identifier];
        }

        // Extract file info from original filename
        $name_without_ext = pathinfo($filename, PATHINFO_FILENAME);
        $extension = pathinfo($filename, PATHINFO_EXTENSION);

        // Use original filename for API (without unique suffix)
        $api_name = $name_without_ext . '.' . $extension;

        // Prepare API data
        $api_data = array(
            'name' => $api_name,
            'file_path' => $file_path
        );

        $last_error = '';

        // Retry logic
        for ($attempt = 1; $attempt <= $retry_attempts; $attempt++) {
            $result = $this->call_upload_api($domain, $api_data);

            if ($result['success']) {
                // Create upload result
                $upload_result = array(
                    'unique_id' => $file_identifier,
                    'filename' => $filename,
                    'success' => true,
                    'domain' => $domain,
                    'attempt' => $attempt,
                    'response' => $result['data'],
                    'group_index' => isset($file['group_index']) ? $file['group_index'] : 0
                );

                // Store in tracker to prevent duplicates
                $this->uploaded_files_tracker[$file_identifier] = $upload_result;

                return $upload_result;
            }

            $last_error = $result['error'];

            // Don't sleep after last attempt
            if ($attempt < $retry_attempts) {
                sleep(1); // Wait 1 second before retry
            }
        }

        // All attempts failed
        $failed_result = array(
            'unique_id' => $file_identifier,
            'filename' => $filename,
            'success' => false,
            'domain' => $domain,
            'attempts' => $retry_attempts,
            'error' => $last_error,
            'group_index' => isset($file['group_index']) ? $file['group_index'] : 0
        );

        // Store failed result in tracker too
        $this->uploaded_files_tracker[$file_identifier] = $failed_result;

        return $failed_result;
    }

    /**
     * Call upload API
     */
    private function call_upload_api($domain, $data) {
        $api_url = rtrim($domain, '/') . '/wp-content/uploads/api/upload.php';

        // Prepare file for upload
        $file_path = $data['file_path'];
        $file_name = $data['name'];

        if (!file_exists($file_path)) {
            return array('success' => false, 'error' => 'File not found: ' . $file_path);
        }

        // Create cURL file
        $curl_file = new CURLFile($file_path, mime_content_type($file_path), $file_name);

        $post_data = array(
            'file' => $curl_file,
            'name' => $file_name
        );

        $ch = curl_init();
        curl_setopt_array($ch, array(
            CURLOPT_URL => $api_url,
            CURLOPT_POST => true,
            CURLOPT_POSTFIELDS => $post_data,
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_SSL_VERIFYPEER => false,
            CURLOPT_USERAGENT => 'WordPress Auto Upload Plugin'
        ));

        $response = curl_exec($ch);
        $http_code = curl_getinfo($ch, CURLINFO_HTTP_CODE);
        $curl_error = curl_error($ch);
        curl_close($ch);

        if ($curl_error) {
            return array('success' => false, 'error' => 'cURL Error: ' . $curl_error);
        }

        if ($http_code !== 200) {
            return array('success' => false, 'error' => 'HTTP Error: ' . $http_code);
        }

        $decoded_response = json_decode($response, true);

        if (json_last_error() !== JSON_ERROR_NONE) {
            return array('success' => false, 'error' => 'Invalid JSON response: ' . $response);
        }

        if (isset($decoded_response['success']) && $decoded_response['success']) {
            return array('success' => true, 'data' => $decoded_response);
        } else {
            $error_msg = isset($decoded_response['message']) ? $decoded_response['message'] : 'Unknown API error';
            return array('success' => false, 'error' => $error_msg);
        }
    }
}
