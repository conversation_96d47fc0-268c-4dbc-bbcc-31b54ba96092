<?php
/**
 * Plugin Name: Auto Upload Image
 * Plugin URI: https://example.com
 * Description: Plugin để upload ảnh tự động lên nhiều domain API với tính năng phân phối và nhóm ảnh thông minh
 * Version: 1.1.2
 * Author: Your Name
 * License: GPL v2 or later
 */

// Prevent direct access
if (!defined('ABSPATH')) {
    exit;
}

// Define plugin constants
define('AUI_PLUGIN_URL', plugin_dir_url(__FILE__));
define('AUI_PLUGIN_PATH', plugin_dir_path(__FILE__));
define('AUI_VERSION', '1.1.2');

// Include required files
require_once AUI_PLUGIN_PATH . 'includes/class-config-manager.php';
require_once AUI_PLUGIN_PATH . 'includes/class-image-uploader.php';
require_once AUI_PLUGIN_PATH . 'includes/class-admin-page.php';

/**
 * Main plugin class
 */
class AutoUploadImage {

    private static $instance = null;
    private $config_manager;
    private $image_uploader;
    private $admin_page;

    /**
     * Get singleton instance
     */
    public static function get_instance() {
        if (self::$instance === null) {
            self::$instance = new self();
        }
        return self::$instance;
    }

    /**
     * Constructor
     */
    private function __construct() {
        $this->init();
    }

    /**
     * Initialize plugin
     */
    private function init() {
        // Initialize components
        $this->config_manager = new AUI_Config_Manager();
        $this->image_uploader = new AUI_Image_Uploader($this->config_manager);
        $this->admin_page = new AUI_Admin_Page($this->config_manager, $this->image_uploader);

        // Hook into WordPress
        add_action('init', array($this, 'load_textdomain'));
        add_action('admin_enqueue_scripts', array($this, 'enqueue_admin_scripts'));

        // Activation/Deactivation hooks
        register_activation_hook(__FILE__, array($this, 'activate'));
        register_deactivation_hook(__FILE__, array($this, 'deactivate'));
    }

    /**
     * Load plugin textdomain
     */
    public function load_textdomain() {
        load_plugin_textdomain('auto-upload-image', false, dirname(plugin_basename(__FILE__)) . '/languages');
    }

    /**
     * Enqueue admin scripts and styles
     */
    public function enqueue_admin_scripts($hook) {
        // Debug: log the hook to see what page we're on
  

        if (strpos($hook, 'auto-upload-image') !== false ||
            strpos($hook, 'toplevel_page_auto-upload-image') !== false ||
            strpos($hook, 'auto-upload-image_page_auto-upload-image-config') !== false) {

            wp_enqueue_style('aui-admin-css', AUI_PLUGIN_URL . 'assets/css/admin.css', array(), AUI_VERSION);
            wp_enqueue_script('aui-admin-js', AUI_PLUGIN_URL . 'assets/js/admin.js', array('jquery'), AUI_VERSION, true);

            // Localize script for AJAX
            wp_localize_script('aui-admin-js', 'aui_ajax', array(
                'ajax_url' => admin_url('admin-ajax.php'),
                'nonce' => wp_create_nonce('aui_nonce'),
                'strings' => array(
                    'uploading' => __('Đang upload...', 'auto-upload-image'),
                    'success' => __('Upload thành công!', 'auto-upload-image'),
                    'error' => __('Có lỗi xảy ra!', 'auto-upload-image'),
                    'retry' => __('Đang thử lại...', 'auto-upload-image')
                )
            ));

     
        }
    }

    /**
     * Plugin activation
     */
    public function activate() {
        // Create default options
        $default_domains = array(
            'https://cn-av.com',
            'https://hentai-sub.com'
        );

        if (!get_option('aui_api_domains')) {
            update_option('aui_api_domains', $default_domains);
        }

        if (!get_option('aui_settings')) {
            $default_settings = array(
                'retry_attempts' => 2,
                'delay_seconds' => 1,
                'auto_delete' => 1
            );
            update_option('aui_settings', $default_settings);
        }
    }

    /**
     * Plugin deactivation
     */
    public function deactivate() {
        // Clean up if needed
    }

    /**
     * Get config manager instance
     */
    public function get_config_manager() {
        return $this->config_manager;
    }

    /**
     * Get image uploader instance
     */
    public function get_image_uploader() {
        return $this->image_uploader;
    }

    /**
     * Get admin page instance
     */
    public function get_admin_page() {
        return $this->admin_page;
    }
}

// Initialize plugin
AutoUploadImage::get_instance();